use anchor_lang::prelude::*;

#[error_code]
pub enum PaymentError {
    #[msg("Insufficient funds for this transaction")]
    InsufficientFunds,

    #[msg("You are not authorized to perform this action")]
    Unauthorized,

    #[msg("Insufficient funds to maintain rent-exempt status")]
    InsufficientFundsForRent,

    #[msg("Account is frozen and cannot perform transactions")]
    AccountFrozen,

    #[msg("Daily spending limit exceeded")]
    SpendLimitExceeded,

    #[msg("Invalid fee rate - must be between 0 and 10000 basis points")]
    InvalidFeeRate,

    #[msg("Fee vault already exists")]
    FeeVaultAlreadyExists,

    #[msg("Fee vault not found")]
    FeeVaultNotFound,

    #[msg("Invalid timestamp for scheduled payment")]
    InvalidScheduledTime,

    #[msg("Scheduled payment not yet due")]
    PaymentNotDue,

    #[msg("Multi-signature requirement not met")]
    MultiSigRequirementNotMet,
}
