use anchor_lang::prelude::*;
use anchor_lang::system_program;
use crate::error::PaymentError;
use crate::{Initialize, InitializeAccount, Deposit, Transfer, Withdraw, InitializeFeeVault, WithdrawFees, SetAccountSettings};

pub fn process_initialize(ctx: Context<Initialize>) -> Result<()> {
    msg!("Greetings from: {:?}", ctx.program_id);
    Ok(())
}


pub fn process_initialize_account(ctx: Context<InitializeAccount>) -> Result<()> {
    let payment_account = &mut ctx.accounts.payment_account;
    payment_account.initialize(ctx.accounts.user.key())?;

    msg!("Payment account initialized for: {:?}", payment_account.owner);
    Ok(())
}

pub fn process_initialize_fee_vault(ctx: Context<InitializeFeeVault>, fee_rate_bps: u16) -> Result<()> {
    let fee_vault = &mut ctx.accounts.fee_vault;
    fee_vault.initialize(ctx.accounts.authority.key(), fee_rate_bps)?;

    msg!("Fee vault initialized with rate: {}bps", fee_rate_bps);
    Ok(())
}


pub fn process_set_account_settings(
    ctx: Context<SetAccountSettings>,
    is_frozen: Option<bool>,
    spend_limit: Option<u64>
) -> Result<()> {
    let payment_account = &mut ctx.accounts.payment_account;

  
    if let Some(frozen) = is_frozen {
        payment_account.is_frozen = frozen;
    }

    if let Some(limit) = spend_limit {
        payment_account.spend_limit = limit;
    }

    msg!("Account settings updated");
    Ok(())
}


pub fn process_deposit(ctx: Context<Deposit>, amount: u64) -> Result<()> {
    
    ctx.accounts.payment_account.check_not_frozen()?;

    let transfer_instruction = system_program::Transfer {
        from: ctx.accounts.user.to_account_info(),
        to: ctx.accounts.payment_account.to_account_info(),
    };

    system_program::transfer(
        CpiContext::new(
            ctx.accounts.system_program.to_account_info(),
            transfer_instruction,
        ),
        amount,
    )?;


    let payment_account = &mut ctx.accounts.payment_account;
    payment_account.add_funds(amount)?;

    msg!("Deposited {} lamports to account", amount);
    Ok(())
}

pub fn process_transfer(ctx: Context<Transfer>, amount: u64) -> Result<()> {
    let payment_account = &mut ctx.accounts.payment_account;
    let recipient_account = &mut ctx.accounts.recipient_account;
    let fee_vault = &mut ctx.accounts.fee_vault;

  
    payment_account.check_not_frozen()?;
    recipient_account.check_not_frozen()?;


    payment_account.check_and_update_spend_limit(amount)?;

 
    require!(payment_account.balance >= amount, PaymentError::InsufficientFunds);


    let fee_amount = fee_vault.calculate_fee(amount);
    let transfer_amount = amount.checked_sub(fee_amount).unwrap();

    payment_account.remove_funds(amount)?;
    recipient_account.add_funds(transfer_amount)?;
    fee_vault.add_fee(fee_amount)?;

   
    let from_lamports = payment_account.to_account_info().lamports();
    let to_lamports = recipient_account.to_account_info().lamports();
    let fee_lamports = fee_vault.to_account_info().lamports();

    **payment_account.to_account_info().try_borrow_mut_lamports()? = from_lamports.checked_sub(amount).unwrap();
    **recipient_account.to_account_info().try_borrow_mut_lamports()? = to_lamports.checked_add(transfer_amount).unwrap();
    **fee_vault.to_account_info().try_borrow_mut_lamports()? = fee_lamports.checked_add(fee_amount).unwrap();

    msg!("Transferred {} lamports to recipient (fee: {})", transfer_amount, fee_amount);
    Ok(())
}

pub fn process_withdraw(ctx: Context<Withdraw>, amount: u64) -> Result<()> {
    let payment_account = &mut ctx.accounts.payment_account;
    let fee_vault = &mut ctx.accounts.fee_vault;

 
    payment_account.check_not_frozen()?;


    payment_account.check_and_update_spend_limit(amount)?;


    require!(payment_account.balance >= amount, PaymentError::InsufficientFunds);

 
    let rent = Rent::get()?;
    let account_info = payment_account.to_account_info();
    let min_balance = rent.minimum_balance(account_info.data_len());


    require!(
        payment_account.balance.checked_sub(amount).unwrap() >= min_balance,
        PaymentError::InsufficientFundsForRent
    );

    let fee_amount = fee_vault.calculate_fee(amount);
    let withdraw_amount = amount.checked_sub(fee_amount).unwrap();

    payment_account.remove_funds(amount)?;
    fee_vault.add_fee(fee_amount)?;

    let from_lamports = payment_account.to_account_info().lamports();
    let to_lamports = ctx.accounts.user.to_account_info().lamports();
    let fee_lamports = fee_vault.to_account_info().lamports();

    **payment_account.to_account_info().try_borrow_mut_lamports()? = from_lamports.checked_sub(amount).unwrap();
    **ctx.accounts.user.to_account_info().try_borrow_mut_lamports()? = to_lamports.checked_add(withdraw_amount).unwrap();
    **fee_vault.to_account_info().try_borrow_mut_lamports()? = fee_lamports.checked_add(fee_amount).unwrap();

    msg!("Withdrew {} lamports from account (fee: {})", withdraw_amount, fee_amount);
    Ok(())
}

pub fn process_withdraw_fees(ctx: Context<WithdrawFees>, amount: u64) -> Result<()> {
    let fee_vault = &mut ctx.accounts.fee_vault;

    require!(fee_vault.fee_balance >= amount, PaymentError::InsufficientFunds);

    **fee_vault.to_account_info().try_borrow_mut_lamports()? -= amount;
    **ctx.accounts.authority.to_account_info().try_borrow_mut_lamports()? += amount;

    fee_vault.withdraw_fees(amount)?;

    msg!("Withdrew {} lamports in fees", amount);
    Ok(())
}
