use anchor_lang::prelude::*;
use super::constants::*;
use super::errors::MultisigError;
use super::state::*;
use std::collections::BTreeMap;

impl MultiSigAccount {
    pub fn space(
        max_signers: usize,
        max_pending_txs: usize,
        max_signatures: usize,
        max_logs: usize,
    ) -> usize {
        let base_size = 8 +
                       32 +
                       32 +
                       1 +
                       1 +
                       8 +
                       8;

        let signers_size = 4 +
                          (32 + 1) * max_signers;

        let role_approvals_size = 4 +
                                 (1 + 1) * 4;

        let signature_size = 4 +
                            32 * max_signatures;

        let log_size = 4 +
                      (32 + 1 + 1 + 8 + 32) * max_logs;

        let pending_tx_size = 4 +
                             (8 +
                              32 +
                              32 +
                              8 +
                              8 +
                              8 +
                              1 +
                              signature_size +
                              log_size) * max_pending_txs;

        base_size + signers_size + role_approvals_size + pending_tx_size
    }

    pub fn initialize(
        &mut self,
        name: [u8; 32],
        payment_account: Pubkey,
        signers: Vec<RoleSigner>,
        threshold: u8,
        role_based_approval: bool,
        min_approvals_per_role: Vec<(u8, u8)>,
    ) -> Result<()> {
        require!(!signers.is_empty(), MultisigError::NoSigners);
        require!(threshold > 0, MultisigError::InvalidThreshold);
        require!(
            threshold as usize <= signers.len(),
            MultisigError::ThresholdTooHigh
        );

        if role_based_approval {
            require!(
                !min_approvals_per_role.is_empty(),
                MultisigError::NoRoleApprovals
            );
        }

        self.name = name;
        self.payment_account = payment_account;
        self.signers = signers;
        self.threshold = threshold;
        self.role_based_approval = role_based_approval;
        self.min_approvals_per_role = min_approvals_per_role;
        self.pending_transactions = Vec::new();
        self.next_tx_id = 1;
        self.created_at = Clock::get()?.unix_timestamp;

        Ok(())
    }

    pub fn is_signer(&self, pubkey: &Pubkey) -> bool {
        self.signers.iter().any(|s| s.signer == *pubkey)
    }

    pub fn get_signer_role(&self, pubkey: &Pubkey) -> Option<u8> {
        self.signers
            .iter()
            .find(|s| s.signer == *pubkey)
            .map(|s| s.role)
    }
}
