use anchor_lang::prelude::*;
use crate::state::PaymentAccount;
use super::state::*;
use super::errors::MultisigError;
use super::constants::*;

/// Accounts required for initializing a new multisig account
#[derive(Accounts)]
pub struct InitializeMultiSig<'info> {
    /// The user creating the multisig account
    #[account(mut)]
    pub creator: Signer<'info>,

    /// The multisig account to be created
    #[account(
        init,
        payer = creator,
        space = 8 + MultiSigAccount::space(
            MAX_SIGNERS,
            MAX_PENDING_TRANSACTIONS,
            MAX_SIGNERS,  // Max signatures = max signers
            MAX_SIGNATURE_LOGS
        ),
    )]
    pub multisig_account: Account<'info, MultiSigAccount>,

    /// The payment account this multisig will control
    pub payment_account: Account<'info, PaymentAccount>,

    /// System program for account creation
    pub system_program: Program<'info, System>,
}

/// Accounts required for proposing a new transaction
#[derive(Accounts)]
pub struct ProposeTransaction<'info> {
    /// The user proposing the transaction (must be a signer on the multisig)
    #[account(mut)]
    pub proposer: Signer<'info>,

    /// The multisig account
    #[account(
        mut,
        constraint = multisig_account.is_signer(&proposer.key()) @ MultisigError::UnauthorizedSigner
    )]
    pub multisig_account: Account<'info, MultiSigAccount>,

    /// The destination account for the transfer
    /// CHECK: This account's public key is stored in the transaction for later validation
    pub destination: AccountInfo<'info>,
}

/// Accounts required for approving a transaction
#[derive(Accounts)]
pub struct ApproveTransaction<'info> {
    /// The user approving the transaction (must be a signer on the multisig)
    #[account(mut)]
    pub approver: Signer<'info>,

    /// The multisig account
    #[account(
        mut,
        constraint = multisig_account.is_signer(&approver.key()) @ MultisigError::UnauthorizedSigner
    )]
    pub multisig_account: Account<'info, MultiSigAccount>,
}

/// Accounts required for executing a transaction
#[derive(Accounts)]
pub struct ExecuteTransaction<'info> {
    /// The user executing the transaction (must be a signer on the multisig)
    #[account(mut)]
    pub executor: Signer<'info>,

    /// The multisig account
    #[account(
        mut,
        constraint = multisig_account.is_signer(&executor.key()) @ MultisigError::UnauthorizedSigner
    )]
    pub multisig_account: Account<'info, MultiSigAccount>,

    /// The payment account controlled by the multisig
    #[account(
        mut,
        constraint = multisig_account.payment_account == payment_account.key() @ MultisigError::InvalidPaymentAccount
    )]
    pub payment_account: Account<'info, PaymentAccount>,

    /// The destination account for the transfer
    /// CHECK: This account is validated in the instruction handler by comparing with the stored destination in the transaction
    #[account(mut)]
    pub destination: AccountInfo<'info>,

    /// System program for transferring funds
    pub system_program: Program<'info, System>,
}

/// Accounts required for cancelling a transaction
#[derive(Accounts)]
pub struct CancelTransaction<'info> {
    /// The user cancelling the transaction (must be a signer on the multisig)
    #[account(mut)]
    pub canceller: Signer<'info>,

    /// The multisig account
    #[account(
        mut,
        constraint = multisig_account.is_signer(&canceller.key()) @ MultisigError::UnauthorizedSigner
    )]
    pub multisig_account: Account<'info, MultiSigAccount>,
}
