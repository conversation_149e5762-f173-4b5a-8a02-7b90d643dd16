
pub const MAX_SIGNERS: usize = 5;


pub const MAX_PENDING_TRANSACTIONS: usize = 5;


pub const MAX_SIGNATURE_LOGS: usize = 5;


pub const ROLE_OWNER: u8 = 0;
pub const ROLE_MANAGER: u8 = 1;
pub const ROLE_FINANCE: u8 = 2;
pub const ROLE_OPERATIONS: u8 = 3;

/// Action types for logging
pub const ACTION_PROPOSE: u8 = 1;
pub const ACTION_APPROVE: u8 = 2;
pub const ACTION_CANCEL: u8 = 3;
pub const ACTION_EXECUTE: u8 = 4;
