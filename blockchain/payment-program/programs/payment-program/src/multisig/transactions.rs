use anchor_lang::prelude::*;
use super::constants::*;
use super::errors::MultisigError;
use super::state::*;
use std::collections::BTreeMap;

impl MultiSigAccount {
    /// Propose a new transaction
    pub fn propose_transaction(
        &mut self,
        proposer: Pubkey,
        destination: Pubkey,
        amount: u64,
        expires_in_seconds: i64,
        memo: [u8; 32],
    ) -> Result<u64> {
        // Validate inputs
        require!(self.is_signer(&proposer), MultisigError::UnauthorizedSigner);
        require!(amount > 0, MultisigError::InvalidAmount);

        // Check if we have too many pending transactions
        require!(
            self.pending_transactions.len() < MAX_PENDING_TRANSACTIONS,
            MultisigError::TooManyPendingTransactions
        );

        // Get current time
        let now = Clock::get()?.unix_timestamp;
        let expires_at = now + expires_in_seconds;

        // Get the proposer's role
        let role = self.get_signer_role(&proposer).unwrap();

        // Create a signature log for the proposal
        let log = SignatureLog {
            signer: proposer,
            role,
            action: ACTION_PROPOSE,
            timestamp: now,
            memo,
        };

        // Create the pending transaction
        let tx_id = self.next_tx_id;
        let pending_tx = PendingTransaction {
            tx_id,
            proposer,
            destination,
            amount,
            created_at: now,
            expires_at,
            status: TransactionStatus::Pending,
            signatures: vec![proposer], // Proposer automatically signs
            logs: vec![log],
        };

        // Add the transaction to the pending list
        self.pending_transactions.push(pending_tx);

        // Increment the transaction ID counter
        self.next_tx_id += 1;

        Ok(tx_id)
    }

    /// Approve a pending transaction
    pub fn approve_transaction(
        &mut self,
        tx_id: u64,
        signer: Pubkey,
        memo: [u8; 32],
    ) -> Result<()> {
        // Validate signer
        require!(self.is_signer(&signer), MultisigError::UnauthorizedSigner);

        // Get the signer's role before borrowing self.pending_transactions
        let role = self.get_signer_role(&signer).unwrap();

        // Find the pending transaction
        let tx_index = self.pending_transactions
            .iter()
            .position(|tx| tx.tx_id == tx_id && tx.status == TransactionStatus::Pending)
            .ok_or(MultisigError::TransactionNotFound)?;

        let tx = &mut self.pending_transactions[tx_index];

        // Check if transaction has expired
        let now = Clock::get()?.unix_timestamp;
        if now > tx.expires_at {
            tx.status = TransactionStatus::Expired;
            return Err(error!(MultisigError::TransactionExpired));
        }

        // Check if signer has already approved
        require!(
            !tx.signatures.contains(&signer),
            MultisigError::AlreadyApproved
        );

        // Create a signature log
        let log = SignatureLog {
            signer,
            role,
            action: ACTION_APPROVE,
            timestamp: now,
            memo,
        };

        // Add the signature and log
        tx.signatures.push(signer);
        tx.logs.push(log);

        Ok(())
    }

    /// Cancel a pending transaction
    pub fn cancel_transaction(
        &mut self,
        tx_id: u64,
        signer: Pubkey,
        memo: [u8; 32],
    ) -> Result<()> {
        // Validate signer
        require!(self.is_signer(&signer), MultisigError::UnauthorizedSigner);

        // Get the signer's role before borrowing self.pending_transactions
        let role = self.get_signer_role(&signer).unwrap();

        // Find the pending transaction
        let tx_index = self.pending_transactions
            .iter()
            .position(|tx| tx.tx_id == tx_id && tx.status == TransactionStatus::Pending)
            .ok_or(MultisigError::TransactionNotFound)?;

        let tx = &mut self.pending_transactions[tx_index];

        // Only the proposer or a signer who has already approved can cancel
        require!(
            tx.proposer == signer || tx.signatures.contains(&signer),
            MultisigError::UnauthorizedCancellation
        );

        // Create a signature log
        let log = SignatureLog {
            signer,
            role,
            action: ACTION_CANCEL,
            timestamp: Clock::get()?.unix_timestamp,
            memo,
        };

        // Update the transaction status and add the log
        tx.status = TransactionStatus::Cancelled;
        tx.logs.push(log);

        Ok(())
    }

    /// Check if a transaction has enough approvals to execute
    pub fn has_enough_approvals(&self, tx_id: u64) -> Result<bool> {
        // Find the pending transaction
        let tx = self.pending_transactions
            .iter()
            .find(|tx| tx.tx_id == tx_id && tx.status == TransactionStatus::Pending)
            .ok_or(MultisigError::TransactionNotFound)?;

        // Check if transaction has expired
        let now = Clock::get()?.unix_timestamp;
        if now > tx.expires_at {
            return Ok(false);
        }

        // Check if we have enough signatures based on threshold
        if tx.signatures.len() < self.threshold as usize {
            return Ok(false);
        }

        // If role-based approval is required, check that too
        if self.role_based_approval {
            // Count approvals by role
            let mut approvals_by_role: BTreeMap<u8, u8> = BTreeMap::new();

            for signer_pubkey in &tx.signatures {
                if let Some(role) = self.get_signer_role(signer_pubkey) {
                    *approvals_by_role.entry(role).or_insert(0) += 1;
                }
            }

            // Check if each role has enough approvals
            for role_approval in &self.min_approvals_per_role {
                let approvals = approvals_by_role.get(&role_approval.role).unwrap_or(&0);
                if *approvals < role_approval.min_approvals {
                    return Ok(false);
                }
            }
        }

        // All checks passed
        Ok(true)
    }

    /// Mark a transaction as executed
    pub fn execute_transaction(
        &mut self,
        tx_id: u64,
        executor: Pubkey,
        memo: [u8; 32],
    ) -> Result<()> {
        // Validate executor
        require!(self.is_signer(&executor), MultisigError::UnauthorizedSigner);

        // Check if transaction has enough approvals before borrowing self.pending_transactions
        let has_enough_approvals = self.has_enough_approvals(tx_id)?;
        require!(has_enough_approvals, MultisigError::NotEnoughApprovals);

        // Get the executor's role before borrowing self.pending_transactions
        let role = self.get_signer_role(&executor).unwrap();

        // Find the pending transaction
        let tx_index = self.pending_transactions
            .iter()
            .position(|tx| tx.tx_id == tx_id && tx.status == TransactionStatus::Pending)
            .ok_or(MultisigError::TransactionNotFound)?;

        let tx = &mut self.pending_transactions[tx_index];

        // Check if transaction has expired
        let now = Clock::get()?.unix_timestamp;
        if now > tx.expires_at {
            tx.status = TransactionStatus::Expired;
            return Err(error!(MultisigError::TransactionExpired));
        }

        // Create a signature log
        let log = SignatureLog {
            signer: executor,
            role,
            action: ACTION_EXECUTE,
            timestamp: now,
            memo,
        };

        // Update the transaction status and add the log
        tx.status = TransactionStatus::Executed;
        tx.logs.push(log);

        Ok(())
    }

    /// Clean up old transactions to save space
    pub fn cleanup_old_transactions(&mut self) -> Result<()> {
        let now = Clock::get()?.unix_timestamp;

        // Remove expired transactions
        self.pending_transactions.retain(|tx| {
            tx.status != TransactionStatus::Pending || now <= tx.expires_at
        });

        // If we still have too many transactions, remove the oldest executed/cancelled ones
        if self.pending_transactions.len() > MAX_PENDING_TRANSACTIONS / 2 {
            // Sort by creation time (oldest first)
            self.pending_transactions.sort_by_key(|tx| tx.created_at);

            // Keep only the most recent ones
            self.pending_transactions.truncate(MAX_PENDING_TRANSACTIONS / 2);
        }

        Ok(())
    }
}
