use anchor_lang::prelude::*;
use super::constants::*;
use super::errors::MultisigError;
use std::collections::BTreeMap;

/// Status of a pending transaction
#[derive(AnchorSerialize, AnchorDeserialize, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
pub enum TransactionStatus {
    /// Transaction is pending and collecting signatures
    Pending,
    /// Transaction has been executed
    Executed,
    /// Transaction has been cancelled
    Cancelled,
    /// Transaction has expired
    Expired,
}

/// A signer with an assigned role
#[derive(AnchorSerialize, AnchorDeserialize, Clone)]
pub struct RoleSigner {
    /// The signer's public key
    pub signer: Pubkey,
    /// The role assigned to this signer
    pub role: u8,
}

/// Role approval requirement
#[derive(AnchorSerialize, AnchorDeserialize, Clone)]
pub struct RoleApproval {
    /// The role ID
    pub role: u8,
    /// Minimum number of approvals required from this role
    pub min_approvals: u8,
}

/// A log entry for a signature action
#[derive(AnchorSerialize, AnchorDeserialize, <PERSON><PERSON>)]
pub struct SignatureLog {
    /// The signer who performed the action
    pub signer: <PERSON><PERSON>,
    /// The role of the signer
    pub role: u8,
    /// The action performed (propose, approve, cancel, execute)
    pub action: u8,
    /// When the action was performed
    pub timestamp: i64,
    /// Optional memo provided with the action
    pub memo: [u8; 32],
}

/// A pending transaction awaiting signatures
#[derive(AnchorSerialize, AnchorDeserialize, Clone)]
pub struct PendingTransaction {
    /// Unique identifier for this transaction
    pub tx_id: u64,
    /// The transaction proposer
    pub proposer: Pubkey,
    /// The destination account for the transfer
    pub destination: Pubkey,
    /// The amount to transfer
    pub amount: u64,
    /// When the transaction was created
    pub created_at: i64,
    /// When the transaction expires
    pub expires_at: i64,
    /// Current status of the transaction
    pub status: TransactionStatus,
    /// Collected signatures (public keys of signers who approved)
    pub signatures: Vec<Pubkey>,
    /// Logs of all actions related to this transaction
    pub logs: Vec<SignatureLog>,
}

/// A multi-signature account that requires multiple approvals for transactions
#[account]
pub struct MultiSigAccount {
    /// Name of this multi-sig account (for identification)
    pub name: [u8; 32],
    /// The payment account this multi-sig controls
    pub payment_account: Pubkey,
    /// All authorized signers with their roles
    pub signers: Vec<RoleSigner>,
    /// Number of signatures required to approve a transaction
    pub threshold: u8,
    /// Whether role-based approval is required
    pub role_based_approval: bool,
    /// Minimum approvals required from each role (if role_based_approval is true)
    pub min_approvals_per_role: Vec<RoleApproval>,
    /// Pending transactions awaiting approval
    pub pending_transactions: Vec<PendingTransaction>,
    /// Next transaction ID to assign
    pub next_tx_id: u64,
    /// When this account was created
    pub created_at: i64,
}
