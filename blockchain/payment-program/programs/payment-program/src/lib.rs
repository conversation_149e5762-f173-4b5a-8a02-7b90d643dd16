use anchor_lang::prelude::*;

declare_id!("CDJfjkmASLweH8BPuBAMRufJGCsHSgwhJcZXfjgZ7Fne");

mod state;
mod error;
mod processors;
mod multisig;

pub use state::*;
pub use error::*;
pub use multisig::*;

#[derive(Accounts)]
pub struct Initialize {}

#[derive(Accounts)]
pub struct InitializeAccount<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        init,
        payer = user,
        space = PaymentAccount::SPACE,
        seeds = [b"payment-account", user.key().as_ref()],
        bump
    )]
    pub payment_account: Account<'info, PaymentAccount>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct InitializeFeeVault<'info> {
    #[account(mut)]
    pub authority: Signer<'info>,

    #[account(
        init,
        payer = authority,
        space = FeeVault::SPACE,
        seeds = [b"fee-vault"],
        bump
    )]
    pub fee_vault: Account<'info, FeeVault>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct SetAccountSettings<'info> {
    #[account(mut)]
    pub owner: Signer<'info>,

    #[account(
        mut,
        seeds = [b"payment-account", owner.key().as_ref()],
        bump,
        constraint = payment_account.owner == owner.key() @ PaymentError::Unauthorized
    )]
    pub payment_account: Account<'info, PaymentAccount>,
}

#[derive(Accounts)]
pub struct Deposit<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        mut,
        seeds = [b"payment-account", user.key().as_ref()],
        bump,
        constraint = payment_account.owner == user.key() @ PaymentError::Unauthorized
    )]
    pub payment_account: Account<'info, PaymentAccount>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct Transfer<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        mut,
        seeds = [b"payment-account", user.key().as_ref()],
        bump,
        constraint = payment_account.owner == user.key() @ PaymentError::Unauthorized
    )]
    pub payment_account: Account<'info, PaymentAccount>,

    #[account(mut)]
    pub recipient_account: Account<'info, PaymentAccount>,

    #[account(
        mut,
        seeds = [b"fee-vault"],
        bump
    )]
    pub fee_vault: Account<'info, FeeVault>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct Withdraw<'info> {
    #[account(mut)]
    pub user: Signer<'info>,

    #[account(
        mut,
        seeds = [b"payment-account", user.key().as_ref()],
        bump,
        constraint = payment_account.owner == user.key() @ PaymentError::Unauthorized
    )]
    pub payment_account: Account<'info, PaymentAccount>,

    #[account(
        mut,
        seeds = [b"fee-vault"],
        bump
    )]
    pub fee_vault: Account<'info, FeeVault>,

    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct WithdrawFees<'info> {
    #[account(mut)]
    pub authority: Signer<'info>,

    #[account(
        mut,
        seeds = [b"fee-vault"],
        bump,
        constraint = fee_vault.authority == authority.key() @ PaymentError::Unauthorized
    )]
    pub fee_vault: Account<'info, FeeVault>,
}

#[program]
pub mod payment_program {
    use super::*;
    use crate::processors::*;

    pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
        process_initialize(ctx)
    }


    pub fn initialize_account(ctx: Context<InitializeAccount>) -> Result<()> {
        process_initialize_account(ctx)
    }

    pub fn initialize_fee_vault(ctx: Context<InitializeFeeVault>, fee_rate_bps: u16) -> Result<()> {
        process_initialize_fee_vault(ctx, fee_rate_bps)
    }

    pub fn set_account_settings(
        ctx: Context<SetAccountSettings>,
        is_frozen: Option<bool>,
        spend_limit: Option<u64>
    ) -> Result<()> {
        process_set_account_settings(ctx, is_frozen, spend_limit)
    }

    pub fn deposit(ctx: Context<Deposit>, amount: u64) -> Result<()> {
        process_deposit(ctx, amount)
    }

    pub fn transfer(ctx: Context<Transfer>, amount: u64) -> Result<()> {
        process_transfer(ctx, amount)
    }

    pub fn withdraw(ctx: Context<Withdraw>, amount: u64) -> Result<()> {
        process_withdraw(ctx, amount)
    }

    pub fn withdraw_fees(ctx: Context<WithdrawFees>, amount: u64) -> Result<()> {
        process_withdraw_fees(ctx, amount)
    }

    // Multisig instructions

    pub fn initialize_multisig(
        ctx: Context<InitializeMultiSig>,
        name: [u8; 32],
        signers: Vec<RoleSigner>,
        threshold: u8,
        role_based_approval: bool,
        min_approvals_per_role: Vec<RoleApproval>,
    ) -> Result<()> {
        multisig::process_initialize_multisig(
            ctx,
            name,
            signers,
            threshold,
            role_based_approval,
            min_approvals_per_role,
        )
    }

    pub fn propose_transaction(
        ctx: Context<ProposeTransaction>,
        amount: u64,
        expires_in_seconds: i64,
        memo: [u8; 32],
    ) -> Result<()> {
        multisig::process_propose_transaction(ctx, amount, expires_in_seconds, memo)
    }

    pub fn approve_transaction(
        ctx: Context<ApproveTransaction>,
        tx_id: u64,
        memo: [u8; 32],
    ) -> Result<()> {
        multisig::process_approve_transaction(ctx, tx_id, memo)
    }

    pub fn execute_transaction(
        ctx: Context<ExecuteTransaction>,
        tx_id: u64,
        memo: [u8; 32],
    ) -> Result<()> {
        multisig::process_execute_transaction(ctx, tx_id, memo)
    }

    pub fn cancel_transaction(
        ctx: Context<CancelTransaction>,
        tx_id: u64,
        memo: [u8; 32],
    ) -> Result<()> {
        multisig::process_cancel_transaction(ctx, tx_id, memo)
    }
}
