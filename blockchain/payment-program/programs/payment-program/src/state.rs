use anchor_lang::prelude::*;

// Account structure to store payment information
#[account]
pub struct PaymentAccount {
    pub owner: Pubkey,      // The account owner's public key
    pub balance: u64,       // Current balance in lamports
    pub created_at: i64,    // Timestamp when the account was created
    pub is_frozen: bool,    // Whether the account is frozen
    pub spend_limit: u64,   // Daily spending limit (0 = no limit)
    pub spent_today: u64,   // Amount spent today
    pub last_spend_reset: i64, // Timestamp of last spend reset
}

// Account structure to store fee collector information
#[account]
pub struct FeeVault {
    pub authority: Pubkey,   // The authority who can withdraw fees
    pub fee_balance: u64,    // Total fees collected
    pub fee_rate_bps: u16,   // Fee rate in basis points (1/100 of 1%)
    pub created_at: i64,     // Timestamp when the vault was created
}

// Implementation of PaymentAccount for additional helper methods
impl PaymentAccount {
    // Calculate the space required for this account
    pub const SPACE: usize = 8 + // discriminator
                             32 + // owner pubkey
                             8 +  // balance
                             8 +  // created_at
                             1 +  // is_frozen
                             8 +  // spend_limit
                             8 +  // spent_today
                             8;   // last_spend_reset

    // Initialize a new payment account
    pub fn initialize(&mut self, owner: Pubkey) -> Result<()> {
        self.owner = owner;
        self.balance = 0;
        self.created_at = Clock::get()?.unix_timestamp;
        self.is_frozen = false;
        self.spend_limit = 0; // No limit by default
        self.spent_today = 0;
        self.last_spend_reset = Clock::get()?.unix_timestamp;
        Ok(())
    }

    // Add funds to the account
    pub fn add_funds(&mut self, amount: u64) -> Result<()> {
        self.balance = self.balance.checked_add(amount).unwrap();
        Ok(())
    }

    // Remove funds from the account
    pub fn remove_funds(&mut self, amount: u64) -> Result<()> {
        self.balance = self.balance.checked_sub(amount).unwrap();
        Ok(())
    }

    // Check if account is frozen
    pub fn check_not_frozen(&self) -> Result<()> {
        require!(!self.is_frozen, crate::error::PaymentError::AccountFrozen);
        Ok(())
    }

    // Check and update spending limit
    pub fn check_and_update_spend_limit(&mut self, amount: u64) -> Result<()> {
        // If no spend limit, allow the transaction
        if self.spend_limit == 0 {
            return Ok(());
        }

        // Check if we need to reset the daily counter
        let current_time = Clock::get()?.unix_timestamp;
        let day_in_seconds = 86400; // 24 hours in seconds

        if current_time - self.last_spend_reset >= day_in_seconds {
            self.spent_today = 0;
            self.last_spend_reset = current_time;
        }

        // Check if this transaction would exceed the daily limit
        let new_spent_today = self.spent_today.checked_add(amount).unwrap();
        require!(new_spent_today <= self.spend_limit, crate::error::PaymentError::SpendLimitExceeded);

        // Update the spent amount
        self.spent_today = new_spent_today;

        Ok(())
    }
}

// Implementation of FeeVault for additional helper methods
impl FeeVault {
    // Calculate the space required for this account
    pub const SPACE: usize = 8 + // discriminator
                             32 + // authority pubkey
                             8 +  // fee_balance
                             2 +  // fee_rate_bps
                             8;   // created_at

    // Initialize a new fee vault
    pub fn initialize(&mut self, authority: Pubkey, fee_rate_bps: u16) -> Result<()> {
        require!(fee_rate_bps <= 10000, crate::error::PaymentError::InvalidFeeRate); // Max 100%
        self.authority = authority;
        self.fee_balance = 0;
        self.fee_rate_bps = fee_rate_bps;
        self.created_at = Clock::get()?.unix_timestamp;
        Ok(())
    }

    // Calculate fee for a given amount
    pub fn calculate_fee(&self, amount: u64) -> u64 {
        // Calculate fee: amount * fee_rate_bps / 10000
        // This gives us the fee in basis points (1/100 of 1%)
        let fee = amount.checked_mul(self.fee_rate_bps as u64).unwrap_or(0).checked_div(10000).unwrap_or(0);
        fee
    }

    // Add fee to the vault
    pub fn add_fee(&mut self, fee_amount: u64) -> Result<()> {
        self.fee_balance = self.fee_balance.checked_add(fee_amount).unwrap();
        Ok(())
    }

    // Withdraw fees from the vault
    pub fn withdraw_fees(&mut self, amount: u64) -> Result<()> {
        require!(amount <= self.fee_balance, crate::error::PaymentError::InsufficientFunds);
        self.fee_balance = self.fee_balance.checked_sub(amount).unwrap();
        Ok(())
    }
}
