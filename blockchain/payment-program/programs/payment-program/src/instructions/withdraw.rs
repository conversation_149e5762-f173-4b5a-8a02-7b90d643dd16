use anchor_lang::prelude::*;
use crate::state::PaymentAccount;
use crate::error::PaymentError;

// Context for withdrawing funds
#[derive(Accounts)]
pub struct Withdraw<'info> {
    #[account(mut)]
    pub user: Signer<'info>,
    
    #[account(
        mut,
        seeds = [b"payment-account", user.key().as_ref()],
        bump,
        constraint = payment_account.owner == user.key() @ PaymentError::Unauthorized
    )]
    pub payment_account: Account<'info, PaymentAccount>,
    
    pub system_program: Program<'info, System>,
}

pub fn withdraw(ctx: Context<Withdraw>, amount: u64) -> Result<()> {
    let payment_account = &mut ctx.accounts.payment_account;
    
    // Check if the account has enough funds
    require!(payment_account.balance >= amount, PaymentError::InsufficientFunds);

    // Calculate rent-exempt amount that must remain in the account
    let rent = Rent::get()?;
    let account_info = payment_account.to_account_info();
    let min_balance = rent.minimum_balance(account_info.data_len());

    // Ensure we maintain rent-exempt balance
    require!(
        payment_account.balance.checked_sub(amount).unwrap() >= min_balance,
        PaymentError::InsufficientFundsForRent
    );

    // Transfer SOL from the payment account to the user
    **payment_account.to_account_info().try_borrow_mut_lamports()? -= amount;
    **ctx.accounts.user.to_account_info().try_borrow_mut_lamports()? += amount;

    // Update the account balance
    payment_account.balance = payment_account.balance.checked_sub(amount).unwrap();
    
    msg!("Withdrew {} lamports from account", amount);
    Ok(())
}
