use anchor_lang::prelude::*;
use anchor_lang::system_program;
use crate::state::PaymentAccount;
use crate::error::PaymentError;

#[derive(Accounts)]
pub struct Deposit<'info> {
    #[account(mut)]
    pub user: Signer<'info>,
    
    #[account(
        mut,
        seeds = [b"payment-account", user.key().as_ref()],
        bump,
        constraint = payment_account.owner == user.key() @ PaymentError::Unauthorized
    )]
    pub payment_account: Account<'info, PaymentAccount>,
    
    pub system_program: Program<'info, System>,
}

pub fn deposit(ctx: Context<Deposit>, amount: u64) -> Result<()> {
    let transfer_instruction = system_program::Transfer {
        from: ctx.accounts.user.to_account_info(),
        to: ctx.accounts.payment_account.to_account_info(),
    };

    system_program::transfer(
        CpiContext::new(
            ctx.accounts.system_program.to_account_info(),
            transfer_instruction,
        ),
        amount,
    )?;

    let payment_account = &mut ctx.accounts.payment_account;
    payment_account.balance = payment_account.balance.checked_add(amount).unwrap();
    
    msg!("Deposited {} lamports to account", amount);
    Ok(())
}
