use anchor_lang::prelude::*;
use crate::state::PaymentAccount;
use crate::error::PaymentError;

// Context for transferring funds
#[derive(Accounts)]
pub struct Transfer<'info> {
    #[account(mut)]
    pub user: Signer<'info>,
    
    #[account(
        mut,
        seeds = [b"payment-account", user.key().as_ref()],
        bump,
        constraint = payment_account.owner == user.key() @ PaymentError::Unauthorized
    )]
    pub payment_account: Account<'info, PaymentAccount>,
    
    #[account(mut)]
    pub recipient_account: Account<'info, PaymentAccount>,
    
    pub system_program: Program<'info, System>,
}

pub fn transfer(ctx: Context<Transfer>, amount: u64) -> Result<()> {
    let payment_account = &mut ctx.accounts.payment_account;
    let recipient_account = &mut ctx.accounts.recipient_account;

    // Check if the sender has enough funds
    require!(payment_account.balance >= amount, PaymentError::InsufficientFunds);

    // Update balances
    payment_account.balance = payment_account.balance.checked_sub(amount).unwrap();
    recipient_account.balance = recipient_account.balance.checked_add(amount).unwrap();
    
    msg!("Transferred {} lamports to recipient", amount);
    Ok(())
}
