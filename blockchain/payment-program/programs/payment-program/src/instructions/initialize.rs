use anchor_lang::prelude::*;
use crate::state::PaymentAccount;

// Context for initializing a new payment account
#[derive(Accounts)]
pub struct Initialize {}

// Context for initializing a new payment account
#[derive(Accounts)]
pub struct InitializeAccount<'info> {
    #[account(mut)]
    pub user: Signer<'info>,
    
    #[account(
        init,
        payer = user,
        space = 8 + 32 + 8 + 8, // discriminator + pubkey + balance + timestamp
        seeds = [b"payment-account", user.key().as_ref()],
        bump
    )]
    pub payment_account: Account<'info, PaymentAccount>,
    
    pub system_program: Program<'info, System>,
}

pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
    msg!("Greetings from: {:?}", ctx.program_id);
    Ok(())
}

pub fn initialize_account(ctx: Context<InitializeAccount>) -> Result<()> {
    let payment_account = &mut ctx.accounts.payment_account;
    payment_account.owner = ctx.accounts.user.key();
    payment_account.balance = 0;
    payment_account.created_at = Clock::get()?.unix_timestamp;
    
    msg!("Payment account initialized for: {:?}", payment_account.owner);
    Ok(())
}
