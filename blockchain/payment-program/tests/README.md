# Test Files

This directory contains test files for the payment program.

## Note on Version Control

The actual test files (`.ts` and `.js`) are excluded from version control because:

1. They can become quite large due to the nature of blockchain testing
2. They often contain generated keypairs and test data
3. They can be regenerated using the test scripts

## Running Tests

To run the tests, use the following command from the project root:

```bash
cd blockchain/payment-program
anchor test
```

## Test Structure

The tests cover the following functionality:

- Basic payment operations (deposit, transfer, withdraw)
- Fee calculations and withdrawals
- Multi-signature functionality
  - Account initialization
  - Transaction proposal
  - Transaction approval
  - Transaction execution
  - Role-based approvals

## Creating New Tests

When creating new tests, follow these guidelines:

1. Create a separate file for each major feature
2. Use descriptive test names
3. Clean up any test accounts or data after the tests complete
4. Avoid hardcoding keypairs or sensitive information
