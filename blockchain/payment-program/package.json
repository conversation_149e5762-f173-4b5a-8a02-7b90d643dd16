{"license": "ISC", "scripts": {"lint:fix": "prettier */*.js \"*/**/*{.js,.ts}\" -w", "lint": "prettier */*.js \"*/**/*{.js,.ts}\" --check", "test": "node -r dotenv/config node_modules/.bin/ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts", "build": "anchor build", "deploy": "anchor deploy", "build:deploy": "npm run build && npm run deploy"}, "dependencies": {"@coral-xyz/anchor": "^0.31.0"}, "devDependencies": {"@types/bn.js": "^5.1.0", "@types/chai": "^4.3.0", "@types/mocha": "^9.0.0", "chai": "^4.3.4", "dotenv": "^16.5.0", "mocha": "^11.1.0", "prettier": "^2.6.2", "ts-mocha": "^10.1.0", "typescript": "^5.7.3"}}